"""
YOLOv8检测器
负责加载模型、执行推理和解析结果
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from loguru import logger

try:
    from ultralytics import YOLO
except ImportError:
    logger.warning("ultralytics未安装，请运行: pip install ultralytics")
    YOLO = None


class YOLODetector:
    """YOLOv8检测器类"""
    
    def __init__(self, model_path: str, conf_threshold: float = 0.5, 
                 img_size: int = 640, device: str = 'auto'):
        """
        初始化检测器
        
        Args:
            model_path: 模型权重文件路径
            conf_threshold: 置信度阈值
            img_size: 输入图像尺寸
            device: 推理设备 ('cpu', 'cuda', 'auto')
        """
        self.model_path = Path(model_path)
        self.conf_threshold = conf_threshold
        self.img_size = img_size
        self.device = device
        self.model = None
        self.class_names = None
        
        self._load_model()
    
    def _load_model(self) -> None:
        """加载YOLO模型"""
        if YOLO is None:
            raise ImportError("ultralytics未安装，无法加载YOLO模型")
        
        if not self.model_path.exists():
            logger.warning(f"模型文件不存在: {self.model_path}")
            logger.info("将使用预训练的YOLOv8n模型")
            self.model = YOLO('yolov8n.pt')
        else:
            logger.info(f"加载模型: {self.model_path}")
            self.model = YOLO(str(self.model_path))
        
        # 获取类别名称
        if hasattr(self.model, 'names'):
            self.class_names = self.model.names
            logger.info(f"检测类别: {self.class_names}")
        else:
            # 默认类别名称（用于自定义模型）
            self.class_names = {0: 'hand', 1: 'product', 2: 'target_container'}
            logger.info(f"使用默认类别名称: {self.class_names}")
    
    def detect(self, image: np.ndarray) -> Dict[str, List[Dict[str, Any]]]:
        """
        执行目标检测
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果字典，格式为:
            {
                'hand': [{'bbox': [x1, y1, x2, y2], 'conf': 0.95}, ...],
                'product': [...],
                'target_container': [...]
            }
        """
        if self.model is None:
            logger.error("模型未加载")
            return {}
        
        try:
            # 执行推理
            results = self.model(
                image,
                conf=self.conf_threshold,
                imgsz=self.img_size,
                device=self.device,
                verbose=False
            )
            
            # 解析结果
            detections = self._parse_results(results[0])
            
            return detections
            
        except Exception as e:
            logger.error(f"检测过程中出错: {e}")
            return {}
    
    def _parse_results(self, result) -> Dict[str, List[Dict[str, Any]]]:
        """
        解析YOLO检测结果
        
        Args:
            result: YOLO检测结果对象
            
        Returns:
            结构化的检测结果
        """
        detections = {
            'hand': [],
            'product': [],
            'target_container': []
        }
        
        if result.boxes is None or len(result.boxes) == 0:
            return detections
        
        # 获取边界框、置信度和类别
        boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
        confidences = result.boxes.conf.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy().astype(int)
        
        # 按类别组织检测结果
        for box, conf, cls in zip(boxes, confidences, classes):
            # 获取类别名称
            if cls in self.class_names:
                class_name = self.class_names[cls]
            else:
                # 尝试映射到我们的目标类别
                if cls == 0:
                    class_name = 'hand'
                elif cls == 1:
                    class_name = 'product'
                elif cls == 2:
                    class_name = 'target_container'
                else:
                    continue  # 跳过未知类别
            
            # 只保留我们关心的类别
            if class_name in detections:
                detection = {
                    'bbox': box.tolist(),  # [x1, y1, x2, y2]
                    'conf': float(conf),
                    'class_id': int(cls)
                }
                detections[class_name].append(detection)
        
        return detections
    
    def draw_detections(self, image: np.ndarray, 
                       detections: Dict[str, List[Dict[str, Any]]],
                       show_conf: bool = True) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果
            show_conf: 是否显示置信度
            
        Returns:
            绘制了检测框的图像
        """
        result_image = image.copy()
        
        # 定义颜色 (BGR格式)
        colors = {
            'hand': (0, 255, 0),        # 绿色
            'product': (255, 0, 0),     # 蓝色
            'target_container': (0, 0, 255)  # 红色
        }
        
        for class_name, objects in detections.items():
            color = colors.get(class_name, (128, 128, 128))
            
            for obj in objects:
                bbox = obj['bbox']
                conf = obj['conf']
                
                # 绘制边界框
                x1, y1, x2, y2 = map(int, bbox)
                cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
                
                # 绘制标签
                label = class_name
                if show_conf:
                    label += f" {conf:.2f}"
                
                # 计算文本尺寸
                (text_width, text_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
                )
                
                # 绘制文本背景
                cv2.rectangle(
                    result_image,
                    (x1, y1 - text_height - 10),
                    (x1 + text_width, y1),
                    color,
                    -1
                )
                
                # 绘制文本
                cv2.putText(
                    result_image,
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (255, 255, 255),
                    2
                )
        
        return result_image
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        info = {
            'model_path': str(self.model_path),
            'conf_threshold': self.conf_threshold,
            'img_size': self.img_size,
            'device': self.device,
            'class_names': self.class_names
        }
        
        return info
