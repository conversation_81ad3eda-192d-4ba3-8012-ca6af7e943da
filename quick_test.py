#!/usr/bin/env python3
"""
YOLOv8快速测试脚本
使用预训练模型直接进行目标检测测试
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ultralytics import YOLO
    from src.utils import setup_logger
    from loguru import logger
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install ultralytics opencv-python loguru")
    sys.exit(1)


def test_yolo_with_camera():
    """使用摄像头测试YOLO模型"""
    print("🎥 使用摄像头测试YOLO模型...")
    
    # 加载模型
    model_path = "scripts/models/yolov8s.pt"
    print(f"📦 加载模型: {model_path}")
    
    try:
        model = YOLO(model_path)
        print("✅ 模型加载成功!")
        print(f"📋 模型类别: {model.names}")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ 无法打开摄像头，尝试使用摄像头1...")
        cap = cv2.VideoCapture(1)
        if not cap.isOpened():
            print("❌ 无法打开任何摄像头")
            return False
    
    print("✅ 摄像头已打开")
    print("💡 控制说明:")
    print("   - 按 'q' 退出")
    print("   - 按 's' 保存当前帧")
    print("   - 按 'SPACE' 暂停/继续")
    
    paused = False
    frame_count = 0
    
    try:
        while True:
            if not paused:
                ret, frame = cap.read()
                if not ret:
                    print("❌ 无法读取摄像头画面")
                    break
                
                frame_count += 1
                
                # YOLO检测
                results = model(frame, conf=0.5, verbose=False)
                
                # 绘制检测结果
                annotated_frame = results[0].plot()
                
                # 添加信息文本
                cv2.putText(annotated_frame, f"Frame: {frame_count}", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(annotated_frame, "Press 'q' to quit, 's' to save, SPACE to pause", 
                           (10, annotated_frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                           0.6, (255, 255, 255), 2)
            
            # 显示画面
            cv2.imshow('YOLOv8 实时检测测试', annotated_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("👋 用户退出")
                break
            elif key == ord('s'):
                # 保存当前帧
                save_path = f"test_frame_{frame_count}.jpg"
                cv2.imwrite(save_path, annotated_frame)
                print(f"💾 已保存: {save_path}")
            elif key == ord(' '):  # 空格键暂停/继续
                paused = not paused
                status = "暂停" if paused else "继续"
                print(f"⏸️ {status}")
    
    except KeyboardInterrupt:
        print("\n🛑 检测到键盘中断")
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print("🧹 资源已清理")
    
    return True


def test_yolo_with_image():
    """使用测试图像测试YOLO模型"""
    print("🖼️ 使用测试图像测试YOLO模型...")
    
    # 加载模型
    model_path = "scripts/models/yolov8s.pt"
    print(f"📦 加载模型: {model_path}")
    
    try:
        model = YOLO(model_path)
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False
    
    # 创建测试图像（如果没有真实图像）
    print("🎨 创建测试图像...")
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 添加一些简单的形状作为测试
    cv2.rectangle(test_image, (100, 100), (200, 200), (255, 0, 0), -1)
    cv2.circle(test_image, (400, 400), 50, (0, 255, 0), -1)
    cv2.putText(test_image, "Test Image", (250, 50), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # YOLO检测
    print("🔍 执行检测...")
    results = model(test_image, conf=0.3, verbose=True)
    
    # 绘制结果
    annotated_image = results[0].plot()
    
    # 保存结果
    output_path = "yolo_test_result.jpg"
    cv2.imwrite(output_path, annotated_image)
    print(f"💾 检测结果已保存: {output_path}")
    
    # 显示结果
    cv2.imshow('YOLOv8 图像检测测试', annotated_image)
    print("👀 按任意键关闭图像窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return True


def main():
    """主函数"""
    setup_logger(level="INFO")
    
    print("🚀 YOLOv8快速测试工具")
    print("=" * 50)
    
    # 检查模型文件
    model_path = Path("scripts/models/yolov8s.pt")
    if not model_path.exists():
        print(f"❌ 模型文件不存在: {model_path}")
        print("请确保模型文件在正确位置")
        return 1
    
    print(f"✅ 找到模型文件: {model_path}")
    
    # 选择测试模式
    print("\n📋 请选择测试模式:")
    print("1. 摄像头实时检测 (推荐)")
    print("2. 测试图像检测")
    print("3. 两种模式都测试")
    
    try:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            success = test_yolo_with_camera()
        elif choice == "2":
            success = test_yolo_with_image()
        elif choice == "3":
            print("\n🔄 先测试图像检测...")
            success1 = test_yolo_with_image()
            print("\n🔄 再测试摄像头检测...")
            success2 = test_yolo_with_camera()
            success = success1 and success2
        else:
            print("❌ 无效选择")
            return 1
        
        if success:
            print("\n🎉 测试完成!")
            print("💡 如果一切正常，您可以运行完整的动作检测系统:")
            print("   python main.py --camera 0")
        else:
            print("\n❌ 测试失败，请检查错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
